# 反爬蟲網頁爬取功能

## 概述

本系統實現了一套完整的反爬蟲策略，用於規避目標網站的反爬蟲機制，成功解決 HTTP 403 Forbidden 錯誤。

## 主要特性

### 1. 瀏覽器偽裝
- **真實瀏覽器引擎**：使用 Playwright 驅動真實的 Chromium 瀏覽器
- **User-Agent 輪換**：支持配置多個 User-Agent 並隨機選擇
- **完整瀏覽器特徵**：設置真實的視窗大小、語言、時區等屬性
- **HTTP Headers 模擬**：添加完整的瀏覽器 headers

### 2. 行為模擬
- **隨機延遲**：在請求間添加可配置的隨機延遲
- **智能重試**：針對 403/429 錯誤實施重試機制
- **頻率控制**：避免過於頻繁的請求

### 3. 技術實現
- **增強啟動參數**：使用多個 Chromium 參數規避自動化檢測
- **上下文隔離**：每個爬取任務使用獨立的瀏覽器上下文
- **錯誤分類處理**：區分不同類型的錯誤並採取相應策略

## 配置說明

### 基本配置
```yaml
system:
  crawling:
    anti_bot:
      min_delay: 1000              # 最小請求間隔（毫秒）
      max_delay: 3000              # 最大請求間隔（毫秒）
      max_retries: 3               # 最大重試次數
      retry_delay: 5000            # 重試間隔（毫秒）
```

### User-Agent 配置
```yaml
system:
  crawling:
    anti_bot:
      user_agents:
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
```

### 代理配置（可選）
```yaml
system:
  crawling:
    anti_bot:
      proxy:
        enabled: true
        servers:
          - "http://proxy1:8080"
          - "http://proxy2:8080"
```

## 使用方法

### 1. API 調用
使用現有的網頁爬取 API，系統會自動應用反爬蟲策略：

```bash
curl -X POST "http://localhost:8086/api/v1/crawl/websites" \
  -H "Content-Type: application/json" \
  -d '{
    "tenant_id": "your-tenant-id",
    "service_id": "your-service-id", 
    "user_id": "your-user-id",
    "web_page_urls": [
      {
        "url": "https://example.com",
        "remark": "測試網站"
      }
    ]
  }'
```

### 2. 配置調整
根據目標網站的反爬蟲強度調整配置：

- **輕度反爬蟲**：使用默認配置即可
- **中度反爬蟲**：增加延遲時間和重試次數
- **重度反爬蟲**：啟用代理輪換，增加更多 User-Agent

## 故障排除

### 常見問題

1. **仍然遇到 403 錯誤**
   - 增加 `min_delay` 和 `max_delay` 值
   - 增加 `max_retries` 次數
   - 添加更多不同的 User-Agent

2. **爬取速度過慢**
   - 減少 `min_delay` 和 `max_delay` 值
   - 但要注意不要觸發反爬蟲機制

3. **代理相關問題**
   - 確保代理服務器可用
   - 檢查代理服務器的認證設置

### 日誌分析
系統會記錄詳細的爬取日誌，包括：
- 使用的 User-Agent
- 請求延遲時間
- 重試次數和原因
- 錯誤狀態碼

## 最佳實踐

1. **合理設置延遲**：根據目標網站的響應時間設置合適的延遲
2. **User-Agent 多樣化**：使用多個不同瀏覽器和版本的 User-Agent
3. **監控成功率**：定期檢查爬取成功率，及時調整策略
4. **遵守 robots.txt**：尊重網站的爬取規則
5. **適度爬取**：避免對目標網站造成過大負載

## 技術細節

### 瀏覽器啟動參數
系統使用了多個 Chromium 啟動參數來規避檢測：
- `--disable-blink-features=AutomationControlled`
- `--disable-web-security`
- `--no-sandbox`
- 等等...

### HTTP Headers
自動添加的 headers 包括：
- `Accept`
- `Accept-Language`
- `Accept-Encoding`
- `Sec-Fetch-*` 系列
- `Upgrade-Insecure-Requests`

這些設置使爬蟲的請求更接近真實瀏覽器的行為。
