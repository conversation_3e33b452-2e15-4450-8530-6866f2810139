2025-05-22T17:07:05.002+0800	WARN	config_client/config_client.go:328	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:07:05.002+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:07:05.002+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:07:05.002+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c6b535f3-bab2-4016-81b6-e04481443a4f)
2025-05-22T17:07:05.002+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:07:05.002+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:07:05.003+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:07:05.003+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c6b535f3-bab2-4016-81b6-e04481443a4f try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:07:05.003+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:07:05.003+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:07:05.003+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:07:05.112+0800	INFO	rpc/rpc_client.go:337	config-0-c6b535f3-bab2-4016-81b6-e04481443a4f success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904825021_192.168.3.3_56566
2025-05-22T17:07:05.852+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:07:05.853+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:07:05.853+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-30972a7e-4377-4cad-9aba-1ac97c73e6ff)
2025-05-22T17:07:05.853+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:07:05.853+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:07:05.853+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:07:05.853+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-30972a7e-4377-4cad-9aba-1ac97c73e6ff try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:07:05.853+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:07:05.853+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:07:05.853+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:07:05.965+0800	INFO	rpc/rpc_client.go:337	config-0-30972a7e-4377-4cad-9aba-1ac97c73e6ff success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904825872_192.168.3.3_56574
2025-05-22T17:09:10.649+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:09:10.649+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:09:10.649+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-66680c2e-3d33-4903-9858-e32034cc0a9a)
2025-05-22T17:09:10.649+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:09:10.649+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:09:10.649+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:09:10.649+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-66680c2e-3d33-4903-9858-e32034cc0a9a try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:09:10.649+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:09:10.649+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:09:10.649+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:09:10.760+0800	INFO	rpc/rpc_client.go:337	config-0-66680c2e-3d33-4903-9858-e32034cc0a9a success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904950667_192.168.3.3_56836
2025-05-22T17:09:10.774+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<[{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-05-22T17:09:39.516+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:09:39.521+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:09:39.523+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:10:32.499+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:10:32.500+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:10:32.500+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6f4e3a8e-ec63-4de1-a450-fec9159be4ae)
2025-05-22T17:10:32.500+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:10:32.500+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:10:32.500+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:10:32.500+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6f4e3a8e-ec63-4de1-a450-fec9159be4ae try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:10:32.500+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:10:32.500+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:10:32.500+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:10:32.611+0800	INFO	rpc/rpc_client.go:337	config-0-6f4e3a8e-ec63-4de1-a450-fec9159be4ae success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905032518_192.168.3.3_56950
2025-05-22T17:10:32.624+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<[{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-05-22T17:11:02.156+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:15:13.303+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:15:13.304+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:15:13.304+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-4b23f85d-a7bb-4973-8ae0-70f5d4ea6fcc)
2025-05-22T17:15:13.304+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:15:13.304+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:15:13.304+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:15:13.304+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-4b23f85d-a7bb-4973-8ae0-70f5d4ea6fcc try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:15:13.304+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:15:13.304+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:15:13.304+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:15:13.415+0800	INFO	rpc/rpc_client.go:337	config-0-4b23f85d-a7bb-4973-8ae0-70f5d4ea6fcc success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905313320_192.168.3.3_57490
2025-05-22T17:15:13.445+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<[{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-05-22T17:15:27.860+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:15:27.864+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:15:27.866+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
2025-05-22T17:18:06.199+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-05-22T17:18:06.199+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-54b68033-8dfd-4d58-9418-eea6c05a2c6a)
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-54b68033-8dfd-4d58-9418-eea6c05a2c6a try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:18:06.199+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:18:06.199+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:18:06.199+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:18:06.310+0800	INFO	rpc/rpc_client.go:337	config-0-54b68033-8dfd-4d58-9418-eea6c05a2c6a success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905486213_192.168.3.3_57820
2025-05-22T17:18:06.325+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<[{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8086,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-05-22T17:18:23.993+0800	INFO	naming_grpc/naming_grpc_proxy.go:121	deregister instance namespaceId:<nanjing-dev>,serviceName:<ams.svc> with instance:<*************:8086@DEFAULT>
