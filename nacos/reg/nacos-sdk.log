2025-05-22T17:07:04.890+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:07:04.891+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55821
2025-05-22T17:07:04.891+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=37b037bc-f050-420e-b724-d71c1da37f5d)
2025-05-22T17:07:04.891+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:07:04.891+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:07:04.891+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:07:04.891+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 37b037bc-f050-420e-b724-d71c1da37f5d try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:07:04.891+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:07:04.891+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:07:04.891+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:07:04.892+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:07:05.002+0800	INFO	rpc/rpc_client.go:337	37b037bc-f050-420e-b724-d71c1da37f5d success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904824911_192.168.3.3_56564
2025-05-22T17:07:05.002+0800	INFO	rpc/rpc_client.go:486	37b037bc-f050-420e-b724-d71c1da37f5d notify connected event to listeners , connectionId=1747904824911_192.168.3.3_56564
2025-05-22T17:07:05.738+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:07:05.738+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55074
2025-05-22T17:07:05.739+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=fc3b8154-94c7-42c1-a1da-7f236a973b22)
2025-05-22T17:07:05.739+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:07:05.739+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:07:05.739+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:07:05.739+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] fc3b8154-94c7-42c1-a1da-7f236a973b22 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:07:05.739+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:07:05.739+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:07:05.739+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:07:05.739+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:07:05.851+0800	INFO	rpc/rpc_client.go:337	fc3b8154-94c7-42c1-a1da-7f236a973b22 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904825758_192.168.3.3_56572
2025-05-22T17:07:05.851+0800	INFO	rpc/rpc_client.go:486	fc3b8154-94c7-42c1-a1da-7f236a973b22 notify connected event to listeners , connectionId=1747904825758_192.168.3.3_56572
2025-05-22T17:09:10.536+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:09:10.537+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55086
2025-05-22T17:09:10.537+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a204a733-8ed4-490f-837d-faf830197ffe)
2025-05-22T17:09:10.537+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:09:10.537+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:09:10.537+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:09:10.537+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a204a733-8ed4-490f-837d-faf830197ffe try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:09:10.537+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:09:10.537+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:09:10.537+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:09:10.537+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:09:10.648+0800	INFO	rpc/rpc_client.go:337	a204a733-8ed4-490f-837d-faf830197ffe success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747904950555_192.168.3.3_56834
2025-05-22T17:09:10.648+0800	INFO	rpc/rpc_client.go:486	a204a733-8ed4-490f-837d-faf830197ffe notify connected event to listeners , connectionId=1747904950555_192.168.3.3_56834
2025-05-22T17:10:32.386+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:10:32.387+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55006
2025-05-22T17:10:32.387+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ff4257ca-80c5-42ad-b391-68101a42b8a6)
2025-05-22T17:10:32.387+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:10:32.387+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:10:32.387+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:10:32.387+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ff4257ca-80c5-42ad-b391-68101a42b8a6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:10:32.387+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:10:32.387+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:10:32.387+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:10:32.387+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:10:32.498+0800	INFO	rpc/rpc_client.go:337	ff4257ca-80c5-42ad-b391-68101a42b8a6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905032404_192.168.3.3_56948
2025-05-22T17:10:32.498+0800	INFO	rpc/rpc_client.go:486	ff4257ca-80c5-42ad-b391-68101a42b8a6 notify connected event to listeners , connectionId=1747905032404_192.168.3.3_56948
2025-05-22T17:15:13.185+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:15:13.187+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55155
2025-05-22T17:15:13.187+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=577fcd1b-3c89-4231-b5ff-c0ea395e7278)
2025-05-22T17:15:13.187+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:15:13.187+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:15:13.187+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:15:13.187+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 577fcd1b-3c89-4231-b5ff-c0ea395e7278 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:15:13.187+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:15:13.187+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:15:13.187+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:15:13.188+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:15:13.300+0800	INFO	rpc/rpc_client.go:337	577fcd1b-3c89-4231-b5ff-c0ea395e7278 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905313204_192.168.3.3_57488
2025-05-22T17:15:13.300+0800	INFO	rpc/rpc_client.go:486	577fcd1b-3c89-4231-b5ff-c0ea395e7278 notify connected event to listeners , connectionId=1747905313204_192.168.3.3_57488
2025-05-22T17:18:06.087+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-05-22T17:18:06.087+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55076
2025-05-22T17:18:06.087+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f6601403-f283-4b8b-935d-cfbe06d499e6)
2025-05-22T17:18:06.087+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-05-22T17:18:06.087+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-05-22T17:18:06.087+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-05-22T17:18:06.087+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f6601403-f283-4b8b-935d-cfbe06d499e6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-05-22T17:18:06.087+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-05-22T17:18:06.087+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-05-22T17:18:06.087+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-05-22T17:18:06.088+0800	INFO	util/common.go:96	Local IP:**********
2025-05-22T17:18:06.198+0800	INFO	rpc/rpc_client.go:337	f6601403-f283-4b8b-935d-cfbe06d499e6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1747905486102_192.168.3.3_57818
2025-05-22T17:18:06.199+0800	INFO	rpc/rpc_client.go:486	f6601403-f283-4b8b-935d-cfbe06d499e6 notify connected event to listeners , connectionId=1747905486102_192.168.3.3_57818
