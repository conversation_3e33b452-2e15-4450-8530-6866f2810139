# https://goframe.org/docs/web/server-config-file-template
server:
  address: ":8000"
  openapiPath: "/api.json"
  swaggerPath: "/swagger"

# https://goframe.org/docs/core/glog-config
logger:
  level: "all"
  stdout: true

# system
system:
  temp_location: ./tmp
  crawling:
    # 爬蟲引擎選擇：colly 或 playwright（建議使用 colly 以獲得更好的性能）
    engine: "colly"

    # 通用配置（兩個引擎共用）- 優化後的設定
    max_depth: 3                    # 降低最大深度避免過深遞歸
    max_pages: 30                   # 降低最大頁面數

    # Colly 專用配置 - 優化設定
    colly:
      # 並發請求數 - 降低以避免被封
      parallel_requests: 1
      # 請求間隔（毫秒）- 增加間隔
      request_delay: 2000
      # 是否啟用緩存
      cache_enabled: true
      # 是否跟隨重定向
      follow_redirects: true
      # 允許的域名（空表示不限制）
      allowed_domains: []
      # HTML 到 Markdown 轉換配置
      html_to_markdown:
        # 轉義模式：smart（智能）、disabled（禁用）
        escape_mode: "smart"
        # 用於相對鏈接轉換的基礎域名（空表示不轉換）
        domain: ""
        # 是否啟用 CommonMark 插件
        commonmark_enabled: true
        # 是否啟用基礎插件
        base_enabled: true

    # Playwright 專用配置
    playwright:
      # 是否在初始化時安裝（只有選擇 playwright 時才安裝）
      install_on_init: false          # 優化：不自動安裝以節省時間
      # 是否無頭模式
      headless: true

    # 反爬蟲配置（兩個引擎共用）- 優化設定
    anti_bot:
      # 請求間隔配置（毫秒）- 增加延遲
      min_delay: 1000
      max_delay: 3000
      # 重試配置 - 增強重試機制
      max_retries: 3
      retry_delay: 5000
      # User-Agent 池 - 擴展更多選項
      user_agents:
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15"
        - "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        - "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        - "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0"
      # 代理配置（可選）
      proxy:
        enabled: false
        servers: []
        # 示例：
        # - "http://proxy1:8080"
        # - "http://proxy2:8080"

data_service:
  name: dsh.svc
  scheme: http


# message queue
rabbitMQ:
  url: amqp://guest:guest@localhost:5672/

storage:
  # gcs: google cloud storage
  # localFS: local storage
  provider: gcs
  # local file system parameters
  localFS:
    path: ./files
  # google cloud storage parameters
  gcs:
    credential_file: ./key.json
    bucket: dev-123
