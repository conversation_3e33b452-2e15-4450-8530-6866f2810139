# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 架构概览

这是一个基于 GoFrame v2 框架的资产管理微服务，使用 Nacos 作为配置中心和服务注册发现。项目遵循标准的分层架构：

- **Controller 层**: 处理HTTP请求，主要位于 `internal/controller/`
- **Service 层**: 业务服务接口，位于 `internal/service/`  
- **Logic 层**: 具体业务逻辑实现，位于 `internal/logic/`
- **Model 层**: 数据模型定义，包含 DO/Entity/DAO，位于 `internal/model/`
- **API 层**: API定义和协议文件，位于 `api/`

## 核心功能模块

- **附件管理**: 支持文件上传、下载、删除等操作
- **资源管理**: 支持多种资源类型 (file, url, plain_text, youtube_link)
- **网页爬取**: 使用 Playwright 进行网页内容抓取
- **消息队列**: 使用 RabbitMQ 进行异步消息处理
- **云存储**: 支持本地文件系统和 Google Cloud Storage

## 常用开发命令

```bash
# 构建应用
make build

# 生成控制器代码
make ctrl

# 生成 DAO/DO/Entity 代码
make dao

# 生成服务层代码
make service

# 生成枚举代码
make enums

# 构建 Docker 镜像
make image

# 构建并推送 Docker 镜像
make image.push

# 部署到 Kubernetes
make deploy

# 解析 protobuf 文件
make pb

# 安装/更新 GoFrame CLI 工具
make cli
```

## 配置管理

- 项目使用 Nacos 进行动态配置管理
- 本地配置文件位于 `manifest/config/config.yaml`
- Nacos 配置会覆盖本地配置
- 支持 `dev` 和 `pro` 两种环境模式

## 服务启动流程

1. `boot/boot.go` 初始化 Nacos 配置和服务注册
2. `main.go` 启动应用程序
3. 通过 `internal/cmd/cmd.go` 处理命令行参数
4. 自动注册服务到 Nacos 注册中心

## 项目依赖

- **GoFrame v2**: 主要 Web 框架
- **Nacos SDK**: 配置中心和服务发现
- **RabbitMQ**: 消息队列
- **Playwright**: 网页自动化和爬取
- **Google Cloud Storage**: 云存储服务
- **Redis**: 缓存服务

## 开发注意事项

- 所有业务逻辑应该在 `internal/logic` 目录下实现
- 使用 `gf gen` 命令自动生成样板代码
- 遵循 GoFrame 的目录结构和编码规范
- 新增 API 时需要在 `api` 目录定义协议文件
- 配置变更应通过 Nacos 管理，避免直接修改本地配置文件

## 服务信息

- 服务名称: `ams.svc` (Asset Management Service)
- 默认部署命名空间: `nanjing-dev`
- 服务组: `DEFAULT_GROUP`