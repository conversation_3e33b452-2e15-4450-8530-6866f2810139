// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package attachments

import (
	"context"

	"assetManagementService/api/attachments/v1"
)

type IAttachmentsV1 interface {
	GetAssets(ctx context.Context, req *v1.GetAssetsReq) (res *v1.GetAssetsRes, err error)
	DownloadFile(ctx context.Context, req *v1.DownloadFileReq) (res *v1.DownloadFileRes, err error)
	UploadFile(ctx context.Context, req *v1.UploadFileReq) (res *v1.UploadFileRes, err error)
	DeleteFile(ctx context.Context, req *v1.DeleteFileReq) (res *v1.DeleteFileRes, err error)
	SetWebSite(ctx context.Context, req *v1.SetWebSiteReq) (res *v1.SetWebSiteRes, err error)
	DeleteWebSite(ctx context.Context, req *v1.DeleteWebSiteReq) (res *v1.DeleteWebSiteRes, err error)
	SetYoutubeLinks(ctx context.Context, req *v1.SetYoutubeLinksReq) (res *v1.SetYoutubeLinksRes, err error)
	DeleteYoutubeLink(ctx context.Context, req *v1.DeleteYoutubeLinkReq) (res *v1.DeleteYoutubeLinkRes, err error)
	SetPlainText(ctx context.Context, req *v1.SetPlainTextReq) (res *v1.SetPlainTextRes, err error)
	DeletePlainText(ctx context.Context, req *v1.DeletePlainTextReq) (res *v1.DeletePlainTextRes, err error)
}
