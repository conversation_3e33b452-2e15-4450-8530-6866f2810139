package v1

import (
	. "assetManagementService/internal/model"
	"assetManagementService/internal/model/resources"
	"assetManagementService/internal/model/website"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
)

type GetAssetsReq struct {
	g.Meta `path:"/v1/attachments/getAssets" tags:"attachments"  method:"post" summary:"get all assets"`
	BaseInfo
}
type GetAssetsRes struct {
	BaseRes
	Assets []*resources.ResourceRecord `json:"assets"`
}

type DownloadFileReq struct {
	g.Meta   `path:"/v1/attachments/download" tags:"attachments"  method:"post" summary:"download file"`
	FilePath string `json:"file_path" v:"required"  dc:"file path"`
}
type DownloadFileRes struct {
	BaseRes
}

// UploadFileReq represents a request structure for uploading files with metadata and associated information.
type UploadFileReq struct {
	g.Meta `path:"/v1/attachments/upload" mime:"multipart/form-data" tags:"attachments"  method:"post" summary:"upload file"`
	File   *ghttp.UploadFiles `json:"file" type:"file"  v:"required"  dc:"uploaded files"`
	BaseInfo
	ContentType string `json:"content_type"  v:"required"  dc:"content type"`
}

type UploadFileRes struct {
	BaseRes
	FileNames []string `json:"file_names"`
}

type DeleteFileReq struct {
	g.Meta    `path:"/v1/attachments/deleteFile" tags:"attachments"  method:"post" summary:"file operator"`
	FileNames []string `json:"file_names"   dc:"file names. Only when deletion is given the file name to be deleted"`
	BaseInfo
}

type DeleteFileRes struct {
	BaseRes
}

// SetWebSiteReq defines the request structure for setting website URLs for a service in a specific tenant context.
type SetWebSiteReq struct {
	g.Meta `path:"/v1/attachments/setWebSite" tags:"attachments"  method:"post" summary:"set website url"`
	BaseInfo
	WebSites []*website.DataWebSite `json:"web_sites" v:"required"  dc:"website urls"`
}

type SetWebSiteRes struct {
	BaseRes
}

// DeleteWebSiteReq defines the request structure for setting website URLs for a service in a specific tenant context.
type DeleteWebSiteReq struct {
	g.Meta  `path:"/v1/attachments/deleteWebSite" tags:"attachments"  method:"post" summary:"website operator"`
	WebSite []string `json:"web_site" v:"required"  dc:"website urls"`
	BaseInfo
}
type DeleteWebSiteRes struct {
	BaseRes
}

// SetYoutubeLinksReq defines the request structure for setting youtube links for a service in a specific tenant context.
type SetYoutubeLinksReq struct {
	g.Meta `path:"/v1/attachments/setYoutubeLink" tags:"attachments"  method:"post" summary:"set youtube links"`
	BaseInfo
	YoutubeContents []*resources.YoutubeContent `json:"youtube_contents" v:"required"  dc:"youtube links"`
}

type SetYoutubeLinksRes struct {
	BaseRes
}

// DeleteYoutubeLinkReq represents the request structure for handling YouTube links operations like list or delete.
type DeleteYoutubeLinkReq struct {
	g.Meta       `path:"/v1/attachments/deleteYoutubeLink" tags:"attachments"  method:"post" summary:"youtube links operator"`
	YoutubeLinks []string `json:"youtube_links" v:"required"  dc:"youtube links"`
	BaseInfo
}
type DeleteYoutubeLinkRes struct {
	BaseRes
}

// SetPlainTextReq defines the request structure for setting plain text for a service in a specific tenant context.
type SetPlainTextReq struct {
	g.Meta `path:"/v1/attachments/setPlainText" tags:"attachments"  method:"post" summary:"set plain text"`
	BaseInfo
	Content string `json:"content" v:"required"  dc:"plain text"`
	Remark  string `json:"remark"  dc:"remark"`
}

type SetPlainTextRes struct {
	BaseRes
}

// DeletePlainTextReq represents a request structure for plain text operations including list and delete actions.
type DeletePlainTextReq struct {
	g.Meta  `path:"/v1/attachments/deletePlainText" tags:"attachments"  method:"post" summary:"plain text operator"`
	Content string `json:"content" v:"required"  dc:"plain text"`
	BaseInfo
}
type DeletePlainTextRes struct {
	BaseRes
}
