package data_saver

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/resources"
	"assetManagementService/internal/service"
	"context"
	"fmt"
	"io"
	"os"

	"cloud.google.com/go/storage"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/grpool"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
)

func init() {
	service.RegisterDataSaver(New())
}

func FailOnError(err error) {
	if err != nil {
		panic(err)
	}
}
func New() service.IDataSaver {
	s := &sDataSaver{
		client: g.Client(),
		worker: grpool.New(),
	}

	ctx := gctx.GetInitCtx()
	s.client.SetDiscovery(gsvc.GetRegistry())

	vProvider, _ := g.Cfg().Get(ctx, "storage.provider", "localFS")

	if gstr.ToLower(vProvider.String()) == consts.GCS {
		err := s.initGCS(ctx)
		FailOnError(err)
	}

	return s
}

type sDataSaver struct {
	worker    *grpool.Pool
	gcsClient *storage.Client
	client    *gclient.Client
}

func (s *sDataSaver) initGCS(ctx context.Context) (err error) {
	if s.gcsClient != nil {
		return
	}

	vCredentialFile, err := g.Cfg().Get(ctx, "storage.gcs.credential_file", "")
	if err != nil {
		return
	}
	if vCredentialFile == nil || vCredentialFile.IsNil() {
		err = gerror.New("storage.gcs.credential_file is nil")
		return
	}
	if !gfile.Exists(vCredentialFile.String()) {
		err = gerror.New("storage.gcs.credential_file is not exist")
		return
	}

	err = os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", vCredentialFile.String())
	if err != nil {
		return
	}

	s.gcsClient, err = storage.NewClient(ctx)
	if err != nil {
		return
	}
	return

}
func (s *sDataSaver) logger() glog.ILogger {
	return g.Log().Cat(consts.CatStorage)
}
func (s *sDataSaver) save(ctx context.Context, baseInfo model.BaseInfo, fileMetaData *data_saver.FileMetadata) (fileContent *resources.FileContent, err error) {

	if fileMetaData == nil {
		err = gerror.New("file metadata is nil")
		return
	}

	vStorageProvider, _ := g.Cfg().Get(ctx, "storage.provider", "localFS")

	switch gstr.ToLower(vStorageProvider.String()) {
	default:
		err = gerror.New("not support storage provider")
		return
	case consts.LocalFS:
		fileContent, err = s.saveToLocalFS(ctx, baseInfo, fileMetaData)
	case consts.GCS:
		fileContent, err = s.saveToGCS(ctx, baseInfo, fileMetaData)
	}

	if err != nil {
		return
	}
	if fileContent == nil {
		err = gerror.New("file content is nil")
		return

	}

	return

}
func (s *sDataSaver) saveToLocalFS(ctx context.Context, bi model.BaseInfo, file *data_saver.FileMetadata) (out *resources.FileContent, err error) {
	s.logger().Debugf(
		ctx,
		"SaveToLocalFS: base information: %v  , file info: %v ",
		gjson.New(bi).MustToJsonString(),
		gjson.New(file).MustToJsonString(),
	)
	vLocalFSPath, _ := g.Cfg().Get(ctx, "storage.localFS.path", "./files")
	newFullFileName := gfile.Join(vLocalFSPath.String(), bi.TenantID, bi.ServiceID, bi.UserID, file.FileName)

	targetDir := gfile.Dir(newFullFileName)
	if !gfile.Exists(targetDir) {
		if err = gfile.Mkdir(targetDir); err != nil {
			s.logger().Error(ctx, err)
			return
		}
	}

	err = gfile.Move(file.FullFileName, newFullFileName)

	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	out = &resources.FileContent{
		FileName:   file.FileName,
		MimeType:   file.MimeType,
		Size:       gfile.Size(newFullFileName),
		AccessPath: newFullFileName,
	}

	return
}
func (s *sDataSaver) DownloadFromGCS(ctx context.Context, fileURL string) (filePathName string, err error) {
	if err = s.initGCS(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	vBucket, _ := g.Cfg().Get(ctx, "storage.gcs.bucket", "storage")
	if vBucket == nil || vBucket.IsNil() {
		err = gerror.New("storage bucket is nil")
		s.logger().Error(ctx, err)
		return
	}
	filePathName = gfile.Join(gfile.Temp("GCS"), gfile.Basename(fileURL))
	bucket := s.gcsClient.Bucket(vBucket.String())
	obj := bucket.Object(gstr.TrimLeftStr(fileURL, "gs://"+vBucket.String()+`/`))

	if obj == nil {
		err = gerror.New("storage object is nil")
		s.logger().Error(ctx, err)
		return
	}
	rc, err := obj.NewReader(ctx)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	defer func(rc *storage.Reader) {
		_ = rc.Close()
	}(rc)

	if data, e := io.ReadAll(rc); e != nil {
		s.logger().Error(ctx, e)
		return "", e
	} else {
		if e = gfile.PutBytes(filePathName, data); e != nil {
			s.logger().Error(ctx, e)
			return "", e
		}
	}

	return
}
func (s *sDataSaver) saveToGCS(ctx context.Context, bi model.BaseInfo, file *data_saver.FileMetadata) (out *resources.FileContent, err error) {
	s.logger().Debugf(
		ctx,
		"SaveToGCS: base information: %v  , file info: %v ",
		gjson.New(bi).MustToJsonString(),
		gjson.New(file).MustToJsonString(),
	)

	if err = s.initGCS(ctx); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	vBucket, _ := g.Cfg().Get(ctx, "storage.gcs.bucket", "storage")
	if vBucket == nil || vBucket.IsNil() {
		err = gerror.New("storage bucket is nil")
		s.logger().Error(ctx, err)
		return
	}
	f, err := os.Open(file.FullFileName)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	defer f.Close()

	fileUriToCloud := fmt.Sprintf("%s/%s/%s/%s", bi.TenantID, bi.ServiceID, bi.UserID, file.FileName)
	bucket := s.gcsClient.Bucket(vBucket.String())
	obj := bucket.Object(fileUriToCloud)
	if obj == nil {
		err = gerror.New("storage object is nil")
		s.logger().Error(ctx, err)
		return
	}
	wc := obj.NewWriter(ctx)

	if _, err = io.Copy(wc, f); err != nil {
		s.logger().Error(ctx, err)
		return
	}

	_ = wc.Close()

	s.logger().Infof(ctx, "save to gcs success: %v", fileUriToCloud)

	out = &resources.FileContent{
		FileName:      file.FileName,
		MimeType:      file.MimeType,
		Size:          gfile.Size(file.FullFileName),
		StorageBucket: vBucket.String(),
		AccessUrl:     fmt.Sprintf("gs://%s/%s", vBucket.String(), fileUriToCloud),
	}

	return

}

func (s *sDataSaver) deleteFileFromLocalFS(ctx context.Context, in *data_saver.DeleteFileInput) (deletedFilesInfo []string, err error) {
	s.logger().Debugf(
		ctx,
		"deleteFileFromLocalFS: %v",
		gjson.New(in).MustToJsonString(),
	)

	deletedFilesInfo = make([]string, 0)
	vLocalFSPath, _ := g.Cfg().Get(ctx, "storage.localFS.path", "./files")

	newFullFileName := gfile.Join(vLocalFSPath.String(), in.TenantID, in.ServiceID)
	if !g.IsEmpty(in.UserID) {
		newFullFileName = gfile.Join(newFullFileName, in.UserID)
	}

	for _, file := range in.Files {
		newFullFileName = gfile.Join(newFullFileName, file)
		deletedFilesInfo = append(deletedFilesInfo, newFullFileName)
		if !gfile.Exists(newFullFileName) {
			s.logger().Debugf(ctx, "The file %q is not exist", newFullFileName)

		} else {

			if e := gfile.RemoveFile(newFullFileName); e != nil {
				s.logger().Debugf(ctx, "Delete file %q error: %v", newFullFileName, e)
			}

		}

	}

	return
}

func (s *sDataSaver) sendDeleteContentsToMQ(ctx context.Context, resourceType string, bi model.BaseInfo, contents []string) {
	var mqDeletedFileData = &data_saver.MQDeleteContentData{
		Schema:    bi.TenantID,
		Table:     fmt.Sprintf(consts.TableResourcesPattern, bi.ServiceID),
		UserID:    bi.UserID,
		ServiceID: bi.ServiceID,
		Contents:  contents,
	}

	if e := service.MessageQueue().Send(
		ctx,
		resourceType,
		consts.ActionDelete,
		[]byte(mqDeletedFileData.String()),
		consts.RouteKeyPayloadChanged,
	); e != nil {
		s.logger().Error(ctx, e)
	}

}

func (s *sDataSaver) deleteFileFromGCS(ctx context.Context, in *data_saver.DeleteFileInput) (deletedFilesInfo []string, err error) {
	s.logger().Debugf(
		ctx,
		"deleteFileFromGCS: %v",
		gjson.New(in).MustToJsonString(),
	)
	if s.gcsClient == nil {
		if err = s.initGCS(ctx); err != nil {
			s.logger().Error(ctx, err)
			return
		}
	}

	vBucket, err := g.Cfg().Get(ctx, "storage.gcs.bucket", "")
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	bucket := s.gcsClient.Bucket(vBucket.String())
	if bucket == nil {
		err = gerror.New("storage bucket is nil")
		s.logger().Error(ctx, err)
		return
	}

	deletedFilesInfo = make([]string, 0)

	for _, file := range in.Files {

		fileUri := fmt.Sprintf("%s/%s/%s/%s", in.TenantID, in.ServiceID, in.UserID, file)
		obj := bucket.Object(fileUri)
		if e := obj.Delete(ctx); e != nil {
			s.logger().Debugf(ctx, "Delete file %q error: %v", fileUri, e)
		} else {
			deletedFilesInfo = append(deletedFilesInfo, fmt.Sprintf("gs://%s/%s", vBucket.String(), fileUri))
		}

	}

	return
}

// sendDataServiceRequest 發送請求到數據服務
func (s *sDataSaver) sendDataServiceRequest(ctx context.Context, endpoint string, requestData interface{}) (responseStr string, err error) {
	vServiceName, _ := g.Cfg().Get(ctx, "system.data_service.name", "dataAccess.svc")
	vScheme, _ := g.Cfg().Get(ctx, "system.data_service.scheme", "http")
	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vServiceName.String(), endpoint)

	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).Post(ctx, url, requestData)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)

	responseStr = response.ReadAllString()
	s.logger().Debugf(ctx, "data service response: %v", responseStr)

	return
}

// ensureTableExists 確保資源表存在，如果不存在則創建
func (s *sDataSaver) ensureTableExists(ctx context.Context, bi model.BaseInfo) (tableName string, err error) {
	tableName = fmt.Sprintf(consts.TableResourcesPattern, bi.ServiceID)

	req := g.Map{
		"schema":  bi.TenantID,
		"raw_sql": fmt.Sprintf(consts.ScriptResource, tableName),
	}

	strResponse, err := s.sendDataServiceRequest(ctx, consts.UriExecuteSQL, req)
	if err != nil {
		return
	}

	if gjson.New(strResponse).Get("code").Int() != consts.Success.Code() {
		err = gerror.New(gjson.New(strResponse).Get("message").String())
		return
	}

	return
}

func (s *sDataSaver) composeResourceRecord(ctx context.Context, bi model.BaseInfo, resourceType string, data any) (record *resources.ResourceRecord, err error) {
	s.logger().Debugf(
		ctx,
		"composeResourceRecord: base information: %v  , resource type: %v , data: %v ",
		gjson.New(bi).MustToJsonString(),
		resourceType,
		gjson.New(data).MustToJsonString(),
	)

	// 確保表存在
	tableName, err := s.ensureTableExists(ctx, bi)
	if err != nil {
		return
	}

	vProvider, _ := g.Cfg().Get(ctx, "storage.provider", "localFS")

	nowTime := gtime.Now()
	record = &resources.ResourceRecord{
		Schema:              bi.TenantID,
		Table:               tableName,
		ServiceID:           bi.ServiceID,
		CreateAT:            nowTime.Time,
		UpdateAT:            nowTime.Time,
		FileStorageProvider: vProvider.String(),
	}

	switch resourceType {
	default:
		err = gerror.New("not support resource type")
		return
	case consts.ResourceTypeFile:
		_ = gconv.Structs(data, &record.UploadFiles)
	case consts.ResourceTypeURL:
		_ = gconv.Structs(data, &record.URLContents)
	case consts.ResourceTypeYoutubeLink:
		_ = gconv.Structs(data, &record.YoutubeContents)
	case consts.ResourceTypePlainText:
		_ = gconv.Structs(data, &record.PlainTextContents)
	}

	return
}

func (s *sDataSaver) SaveFile(ctx context.Context, in *data_saver.SaveFileInput) (err error) {

	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}

	s.logger().Infof(ctx, "SaveFile: %v", gjson.New(in).MustToJsonIndentString())
	var _in = in
	_ = s.worker.AddWithRecover(gctx.NeverDone(ctx), func(ctx context.Context) {

		var fileContents = make([]*resources.FileContent, 0)
		for _, content := range _in.FileContents {
			fileContent, err := s.save(ctx, _in.BaseInfo, content)
			if err == nil && fileContent != nil {
				fileContents = append(fileContents, fileContent)
			} else {
				panic(err)
			}
		}

		// 組合成 resource  record 發給MQ 後續寫入到資料庫or vector db
		record, err := s.composeResourceRecord(ctx, _in.BaseInfo, consts.ResourceTypeFile, fileContents)
		if err != nil {
			panic(err)
		} else {
			// 調用Mq發送出去
			err = service.MessageQueue().Send(
				ctx,
				consts.ResourceTypeFile,
				consts.ActionUpdateOrInsert,
				[]byte(record.String()),
				consts.RouteKeyPayloadChanged,
			)
			if err != nil {
				panic(err)
			}
		}

	}, func(ctx context.Context, exception error) {

		s.logger().Error(ctx, exception)
	})

	return
}

func (s *sDataSaver) SaveURLContent(ctx context.Context, in *data_saver.SaveWebPageInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "SaveURLContent: %v", gjson.New(in).MustToJsonIndentString())
	var _in = in
	_ = s.worker.AddWithRecover(gctx.NeverDone(ctx), func(ctx context.Context) {
		var webUrlContents = make([]*resources.WebSiteURLContent, 0)
		for _, urlContent := range _in.WebSiteURLs {
			var webUrlContent = &resources.WebSiteURLContent{
				WebSiteURL:        urlContent.DataWebSite.URL,
				PageToFileContent: make(map[string]*resources.FileContent),
			}

			for pageUrl, mdFileName := range urlContent.WebPageFile {

				fileContent, err := s.save(ctx, _in.BaseInfo, &data_saver.FileMetadata{
					FileName:     gfile.Basename(mdFileName),
					FullFileName: mdFileName,
					MimeType:     "text/md",
				})

				if err == nil {
					webUrlContent.PageToFileContent[pageUrl] = fileContent
				} else {
					s.logger().Error(ctx, err)
				}

			}
			webUrlContents = append(webUrlContents, webUrlContent)

		}

		record, err := s.composeResourceRecord(ctx, _in.BaseInfo, consts.ResourceTypeURL, webUrlContents)

		if err != nil {
			panic(err)
		} else {
			if err = service.MessageQueue().Send(
				ctx,
				consts.ResourceTypeURL,
				consts.ActionUpdateOrInsert,
				[]byte(record.String()),
				consts.RouteKeyPayloadChanged,
			); err != nil {
				panic(err)
			}
		}

	}, func(ctx context.Context, exception error) {
		s.logger().Error(ctx, exception)
	})

	return
}

func (s *sDataSaver) SavePlainTextContent(ctx context.Context, in *data_saver.SavePlainTextInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "SavePlainTextContent: %v", gjson.New(in).MustToJsonIndentString())
	record, err := s.composeResourceRecord(ctx, in.BaseInfo, consts.ResourceTypePlainText, in.PlainTextContents)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	if err = service.MessageQueue().Send(
		ctx,
		consts.ResourceTypePlainText,
		consts.ActionUpdateOrInsert,
		[]byte(record.String()),
		consts.RouteKeyPayloadChanged,
	); err != nil {
		s.logger().Error(ctx, err)
	}

	return
}
func (s *sDataSaver) SaveYoutubeContent(ctx context.Context, in *data_saver.SaveYoutubeInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "SaveYoutubeContent: %v", gjson.New(in).MustToJsonIndentString())
	record, err := s.composeResourceRecord(ctx, in.BaseInfo, consts.ResourceTypeYoutubeLink, in.YoutubeContents)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if err = service.MessageQueue().Send(
		ctx,
		consts.ResourceTypeYoutubeLink,
		consts.ActionUpdateOrInsert,
		[]byte(record.String()),
		consts.RouteKeyPayloadChanged,
	); err != nil {
		s.logger().Error(ctx, err)
	}
	return
}
func (s *sDataSaver) DeleteFile(ctx context.Context, in *data_saver.DeleteFileInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "DeleFile: %v", gjson.New(in).MustToJsonIndentString())
	// delete file from  local FS or gcs
	vStorageProvider, _ := g.Cfg().Get(ctx, "storage.provider", "localFS")
	var deletedFilesInfo []string
	switch gstr.ToLower(vStorageProvider.String()) {
	default:
		err = gerror.New("not support storage provider")
	case consts.LocalFS:
		deletedFilesInfo, err = s.deleteFileFromLocalFS(ctx, in)
	case consts.GCS:
		deletedFilesInfo, err = s.deleteFileFromGCS(ctx, in)
	}

	if err != nil {
		s.logger().Error(ctx, err)
	} else if len(deletedFilesInfo) > 0 {
		s.sendDeleteContentsToMQ(ctx, consts.ResourceTypeFile, in.BaseInfo, deletedFilesInfo)
	}

	return
}

func (s *sDataSaver) DeletePlainText(ctx context.Context, in *data_saver.DeletePlainTextInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}

	s.logger().Infof(ctx, "DeletePlainText: %v", gjson.New(in).MustToJsonIndentString())

	s.sendDeleteContentsToMQ(ctx, consts.ResourceTypePlainText, in.BaseInfo, []string{in.PlainTextContent})

	return
}

func (s *sDataSaver) DeleteYoutubeLinks(ctx context.Context, in *data_saver.DeleteYoutubeLinksInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "DeleteYoutubeLinks: %v", gjson.New(in).MustToJsonIndentString())

	s.sendDeleteContentsToMQ(ctx, consts.ResourceTypeYoutubeLink, in.BaseInfo, in.YoutubeLinks)

	return
}

func (s *sDataSaver) DeleteWebSiteUrls(ctx context.Context, in *data_saver.DeleteWebSiteInput) (err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}
	s.logger().Infof(ctx, "DeleteWebSiteUrls: %v", gjson.New(in).MustToJsonIndentString())
	where := "service_id = ? "
	params := g.Slice{in.ServiceID}

	req := &data_saver.GetContentsReq{
		Schema:    in.TenantID,
		Table:     fmt.Sprintf(consts.TableResourcesPattern, in.ServiceID),
		WhereCond: where,
		Params:    params,
		Fields:    g.Slice{"url_contents"},
	}

	strResponse, err := s.sendDataServiceRequest(ctx, consts.UriGetAllContent, req)
	if err != nil {
		return
	}
	if !gjson.Valid(strResponse) {
		s.logger().Debugf(ctx, "response is not valid json: %s", strResponse)
		err = gerror.NewCode(consts.Failed)
		return
	}
	var res *data_saver.GetContentsRes
	_ = gjson.New(strResponse).Scan(&res)

	if res == nil {
		s.logger().Debugf(ctx, "response is not valid json: %s", strResponse)
		err = gerror.NewCode(consts.Failed)
	} else {
		gsFiles := make([]string, 0)
		localFiles := make([]string, 0)

		var webSiteURLContents []*resources.WebSiteURLContent
		// only one record
		if len(res.Contents) == 0 {
			err = gerror.NewCode(consts.Failed)
			s.logger().Debugf(ctx, "can not find record: %v", res.Contents)
			return
		}

		_ = gconv.Structs(res.Contents[0]["url_contents"], &webSiteURLContents)
		if len(webSiteURLContents) == 0 {
			err = gerror.NewCode(consts.Failed)
			s.logger().Debugf(ctx, "can not find record: %v", res.Contents)
			return
		}
		var urlToUrlContents = gmap.NewStrAnyMap(true)
		for _, content := range webSiteURLContents {
			urlToUrlContents.Set(content.WebSiteURL, content)
		}
		var urlArray = garray.NewStrArrayFrom(urlToUrlContents.Keys())

		for _, siteURL := range in.WebSiteURLs {
			if urlArray.Contains(siteURL) {
				var urlContent *resources.WebSiteURLContent
				_ = urlToUrlContents.GetVar(siteURL).Struct(&urlContent)
				if urlContent != nil {
					for _, content := range urlContent.PageToFileContent {
						if !g.IsEmpty(content.AccessUrl) && gstr.HasPrefix(content.AccessUrl, "gs:") {
							gsFiles = append(gsFiles, content.AccessUrl)
						}
						if !g.IsEmpty(content.AccessPath) {
							localFiles = append(localFiles, content.AccessPath)
						}
					}
				}
			}
		}

		fnLogError := func(err error) {
			if err != nil {
				s.logger().Error(ctx, err)
			}
		}

		if len(gsFiles) > 0 {
			s.logger().Debugf(ctx, "delete file from gcs: %v", gsFiles)
			_, e := s.deleteFileFromGCS(ctx, &data_saver.DeleteFileInput{
				BaseInfo: in.BaseInfo,
				Files:    gsFiles,
			})
			fnLogError(e)

		}
		if len(localFiles) > 0 {
			s.logger().Debugf(ctx, "delete file from localFS: %v", localFiles)
			_, e := s.deleteFileFromLocalFS(ctx, &data_saver.DeleteFileInput{
				BaseInfo: in.BaseInfo,
				Files:    localFiles,
			})
			fnLogError(e)

		}

		s.sendDeleteContentsToMQ(ctx, consts.ResourceTypeURL, in.BaseInfo, in.WebSiteURLs)
	}

	return
}

func (s *sDataSaver) GetAssets(ctx context.Context, in *model.BaseInfo) (resources []*resources.ResourceRecord, err error) {
	if in == nil {
		err = gerror.New("the input parameter is nil")
		s.logger().Error(ctx, err)
		return
	}

	params := g.Slice{in.ServiceID}
	where := "service_id = ? "

	req := &data_saver.GetContentsReq{
		Schema:    in.TenantID,
		Table:     fmt.Sprintf(consts.TableResourcesPattern, in.ServiceID),
		WhereCond: where,
		Params:    params,
		Fields:    g.Slice{"*"},
	}

	strResponse, err := s.sendDataServiceRequest(ctx, consts.UriGetAllContent, req)
	if err != nil {
		return
	}
	if !gjson.Valid(strResponse) {
		s.logger().Debugf(ctx, "response is not valid json: %s", strResponse)
		err = gerror.NewCode(consts.Failed)
		return
	}
	var res *data_saver.GetContentsRes
	_ = gjson.New(strResponse).Scan(&res)
	if res == nil {
		s.logger().Debugf(ctx, "response is not valid json: %s", strResponse)
		err = gerror.NewCode(consts.Failed)
		return
	}
	err = gconv.Structs(res.Contents, &resources)
	return
}
