2025-08-14T18:34:00.087+0800	WARN	config_client/config_client.go:328	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:34:00.087+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:34:00.087+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-14T18:34:00.087+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-23c582f4-e979-4cd0-ac97-a868ded3ecbc)
2025-08-14T18:34:00.087+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:34:00.087+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:34:00.087+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:34:00.087+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-23c582f4-e979-4cd0-ac97-a868ded3ecbc try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:34:00.087+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:34:00.087+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:34:00.087+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:34:00.196+0800	INFO	rpc/rpc_client.go:337	config-0-23c582f4-e979-4cd0-ac97-a868ded3ecbc success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167642262_192.168.3.3_64509
2025-08-14T18:37:26.429+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:37:26.430+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-14T18:37:26.430+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0f89c633-5736-4de9-a3de-c21e76955df7)
2025-08-14T18:37:26.430+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:37:26.430+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:37:26.430+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:37:26.430+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0f89c633-5736-4de9-a3de-c21e76955df7 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:37:26.430+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:37:26.430+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:37:26.430+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:37:26.540+0800	INFO	rpc/rpc_client.go:337	config-0-0f89c633-5736-4de9-a3de-c21e76955df7 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167848604_192.168.3.3_65053
2025-08-14T18:37:33.260+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:37:33.260+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-14T18:37:33.260+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-6e71854e-feaf-4b8a-914e-744252f49a63)
2025-08-14T18:37:33.260+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:37:33.260+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:37:33.260+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:37:33.260+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-6e71854e-feaf-4b8a-914e-744252f49a63 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:37:33.260+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:37:33.260+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:37:33.260+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:37:33.370+0800	INFO	rpc/rpc_client.go:337	config-0-6e71854e-feaf-4b8a-914e-744252f49a63 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167855434_192.168.3.3_65073
2025-08-14T18:38:44.369+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:38:44.370+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-14T18:38:44.370+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9d371b32-3c47-474c-bc2e-2749a39de425)
2025-08-14T18:38:44.370+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:38:44.370+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:38:44.370+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:38:44.370+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9d371b32-3c47-474c-bc2e-2749a39de425 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:38:44.370+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:38:44.370+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:38:44.370+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:38:44.482+0800	INFO	rpc/rpc_client.go:337	config-0-9d371b32-3c47-474c-bc2e-2749a39de425 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167926547_192.168.3.3_65288
2025-08-14T18:40:05.019+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/ams.yaml@@DEFAULT_GROUP@@nanjing-dev.: file not exist
2025-08-14T18:40:05.019+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/ams.yaml@@DEFAULT_GROUP@@nanjing-dev_failover.
2025-08-14T18:40:05.019+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-bfc6d90c-3dcf-478d-b2a6-5e1821b9ac72)
2025-08-14T18:40:05.019+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:40:05.019+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:40:05.019+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:40:05.019+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-bfc6d90c-3dcf-478d-b2a6-5e1821b9ac72 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:40:05.019+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:40:05.019+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:40:05.019+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:40:05.130+0800	INFO	rpc/rpc_client.go:337	config-0-bfc6d90c-3dcf-478d-b2a6-5e1821b9ac72 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755168007194_192.168.3.3_65482
