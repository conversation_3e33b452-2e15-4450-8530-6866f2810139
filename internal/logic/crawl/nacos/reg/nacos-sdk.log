2025-08-14T18:33:59.973+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:33:59.973+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55107
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=4ae3a8d7-b4d2-455d-a188-cd65570a50e5)
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:33:59.973+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 4ae3a8d7-b4d2-455d-a188-cd65570a50e5 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:33:59.974+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:33:59.974+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:34:00.086+0800	INFO	rpc/rpc_client.go:337	4ae3a8d7-b4d2-455d-a188-cd65570a50e5 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167642152_192.168.3.3_64507
2025-08-14T18:34:00.086+0800	INFO	rpc/rpc_client.go:486	4ae3a8d7-b4d2-455d-a188-cd65570a50e5 notify connected event to listeners , connectionId=1755167642152_192.168.3.3_64507
2025-08-14T18:37:26.316+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:37:26.316+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55933
2025-08-14T18:37:26.316+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a8b27c1c-da71-4266-aa0b-b8537b8596ff)
2025-08-14T18:37:26.316+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:37:26.316+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:37:26.316+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:37:26.316+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a8b27c1c-da71-4266-aa0b-b8537b8596ff try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:37:26.316+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:37:26.316+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:37:26.316+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:37:26.317+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:37:26.427+0800	INFO	rpc/rpc_client.go:337	a8b27c1c-da71-4266-aa0b-b8537b8596ff success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167848491_192.168.3.3_65051
2025-08-14T18:37:33.151+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:37:33.152+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55646
2025-08-14T18:37:33.152+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b4e03a6c-ba8c-4194-b296-bb087387c620)
2025-08-14T18:37:33.152+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:37:33.152+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:37:33.152+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:37:33.152+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b4e03a6c-ba8c-4194-b296-bb087387c620 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:37:33.152+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:37:33.152+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:37:33.152+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:37:33.153+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:37:33.259+0800	INFO	rpc/rpc_client.go:337	b4e03a6c-ba8c-4194-b296-bb087387c620 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167855325_192.168.3.3_65071
2025-08-14T18:37:33.259+0800	INFO	rpc/rpc_client.go:486	b4e03a6c-ba8c-4194-b296-bb087387c620 notify connected event to listeners , connectionId=1755167855325_192.168.3.3_65071
2025-08-14T18:38:44.256+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:38:44.257+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55276
2025-08-14T18:38:44.257+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d4954ad3-361e-4695-a08f-16bfb777c6ab)
2025-08-14T18:38:44.257+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:38:44.257+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:38:44.257+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:38:44.257+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d4954ad3-361e-4695-a08f-16bfb777c6ab try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:38:44.257+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:38:44.257+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:38:44.257+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:38:44.258+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:38:44.369+0800	INFO	rpc/rpc_client.go:337	d4954ad3-361e-4695-a08f-16bfb777c6ab success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755167926433_192.168.3.3_65285
2025-08-14T18:38:44.369+0800	INFO	rpc/rpc_client.go:486	d4954ad3-361e-4695-a08f-16bfb777c6ab notify connected event to listeners , connectionId=1755167926433_192.168.3.3_65285
2025-08-14T18:40:04.902+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/nanjing-dev failed!err:open ./nacos/reg/naming/nanjing-dev: no such file or directory
2025-08-14T18:40:04.902+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55058
2025-08-14T18:40:04.902+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c23366b9-644b-425a-97cf-77f1caa803e6)
2025-08-14T18:40:04.902+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-08-14T18:40:04.902+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-08-14T18:40:04.902+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-08-14T18:40:04.902+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c23366b9-644b-425a-97cf-77f1caa803e6 try to connect to server on start up, server: {serverIp:************ serverPort:8848 serverGrpcPort:9848}
2025-08-14T18:40:04.902+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-08-14T18:40:04.902+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-08-14T18:40:04.902+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-08-14T18:40:04.903+0800	INFO	util/common.go:96	Local IP:************
2025-08-14T18:40:05.018+0800	INFO	rpc/rpc_client.go:337	c23366b9-644b-425a-97cf-77f1caa803e6 success to connect to server {serverIp:************ serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1755168007082_192.168.3.3_65480
2025-08-14T18:40:05.018+0800	INFO	rpc/rpc_client.go:486	c23366b9-644b-425a-97cf-77f1caa803e6 notify connected event to listeners , connectionId=1755168007082_192.168.3.3_65480
