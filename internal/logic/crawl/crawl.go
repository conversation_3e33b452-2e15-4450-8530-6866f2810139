package crawl

import (
	"assetManagementService/boot"
	"assetManagementService/internal/service"

	"github.com/gogf/gf/v2/os/gctx"
)

func init() {
	ctx := gctx.GetInitCtx()
	boot.WaitReady()
	// 初始化爬蟲引擎（條件性安裝 Playwright）
	err := InitializeCrawlerEngine(ctx)
	if err != nil {
		logger().Errorf(ctx, "Failed to initialize crawler engine: %v", err)
		panic(err)
	}

	// 註冊爬蟲服務
	service.RegisterCrawler(NewCrawler(ctx))
	logger().Infof(ctx, "Crawler service registered successfully")
}
