package crawl

import (
	"assetManagementService/internal/service"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/playwright-community/playwright-go"
)

// CrawlerEngine 爬蟲引擎類型
type CrawlerEngine string

const (
	EnginePlaywright CrawlerEngine = "playwright"
	EngineColly      CrawlerEngine = "colly"
)

// NewCrawler 根據配置創建相應的爬蟲實例
func NewCrawler(ctx context.Context) service.ICrawler {
	// 從配置中獲取引擎類型，默認使用 playwright 確保向後兼容
	engineConfig, _ := g.Cfg().Get(ctx, "system.crawling.engine", "playwright")
	engine := CrawlerEngine(engineConfig.String())

	logger().Infof(ctx, "Creating crawler with engine: %s", engine)

	switch engine {
	case EngineColly:
		return newCollyCrawler(ctx)
	case EnginePlaywright:
		return newPlaywrightCrawler(ctx)
	default:
		// 默認使用 playwright 確保兼容性
		logger().Warningf(ctx, "Unknown crawler engine: %s, falling back to playwright", engine)
		return newPlaywrightCrawler(ctx)
	}
}

// shouldInstallPlaywright 檢查是否需要安裝 Playwright
func shouldInstallPlaywright(ctx context.Context) bool {
	// 獲取引擎配置
	engineConfig, _ := g.Cfg().Get(ctx, "system.crawling.engine", "playwright")
	engine := CrawlerEngine(engineConfig.String())

	// 只有當引擎是 playwright 時才需要安裝
	if engine != EnginePlaywright {
		return false
	}

	// 檢查 Playwright 配置中的 install_on_init 選項
	playwrightConfig := loadPlaywrightConfig(ctx)
	return playwrightConfig.InstallOnInit
}

// InitializeCrawlerEngine 初始化爬蟲引擎（在 init() 函數中調用）
func InitializeCrawlerEngine(ctx context.Context) error {
	// 只有在需要時才安裝 Playwright
	if shouldInstallPlaywright(ctx) {
		logger().Infof(ctx, "Installing Playwright...")
		err := playwright.Install()
		if err != nil {
			logger().Errorf(ctx, "Failed to install Playwright: %v", err)
			return err
		}
		logger().Infof(ctx, "Playwright installed successfully")
	} else {
		logger().Infof(ctx, "Skipping Playwright installation (not required for current engine)")
	}

	return nil
}
