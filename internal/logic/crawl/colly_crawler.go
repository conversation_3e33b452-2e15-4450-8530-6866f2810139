package crawl

import (
	"assetManagementService/internal/model/crawl"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/website"
	"assetManagementService/internal/service"
	"context"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/<PERSON>/html-to-markdown/v2/converter"
	"github.com/<PERSON>/html-to-markdown/v2/plugin/base"
	"github.com/<PERSON>/html-to-markdown/v2/plugin/commonmark"
	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/debug"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/grpool"
)

// sCollyCrawler Colly 爬蟲實現
type sCollyCrawler struct {
	collector  *colly.Collector
	converter  *converter.Converter
	workerPool *grpool.Pool
}

// newCollyCrawler 創建 Colly 爬蟲實例
func newCollyCrawler(ctx context.Context) service.ICrawler {
	s := &sCollyCrawler{
		workerPool: grpool.New(),
	}

	// 載入配置
	collyConfig := loadCollyConfig(ctx)
	antiBotConfig := loadAntiBotConfig(ctx)

	// 設置 Colly 收集器
	s.setupCollector(ctx, collyConfig, antiBotConfig)

	// 設置 HTML 到 Markdown 轉換器
	s.setupConverter(ctx, collyConfig)

	logger().Infof(ctx, "Colly crawler initialized successfully")
	return s
}

// setupCollector 設置 Colly 收集器
func (s *sCollyCrawler) setupCollector(ctx context.Context, collyConfig *CollyConfig, antiBotConfig *AntiBotConfig) {
	// 創建收集器
	s.collector = colly.NewCollector(
		colly.Debugger(&debug.LogDebugger{}),
	)

	// 設置允許的域名
	if len(collyConfig.AllowedDomains) > 0 {
		s.collector.AllowedDomains = collyConfig.AllowedDomains
	}

	// 設置並發限制和延遲
	s.collector.Limit(&colly.LimitRule{
		DomainGlob:  "*",
		Parallelism: collyConfig.ParallelRequests,
		Delay:       time.Duration(collyConfig.RequestDelay) * time.Millisecond,
	})

	// 設置 User-Agent
	s.collector.UserAgent = getRandomUserAgent(antiBotConfig)

	// 設置請求回調
	s.collector.OnRequest(func(r *colly.Request) {
		// 隨機設置 User-Agent
		r.Headers.Set("User-Agent", getRandomUserAgent(antiBotConfig))

		// 設置額外的 HTTP headers 來模擬真實瀏覽器
		r.Headers.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7")
		r.Headers.Set("Accept-Language", "zh-TW,zh;q=0.9,en;q=0.8,zh-CN;q=0.7")
		r.Headers.Set("Accept-Encoding", "gzip, deflate, br")
		r.Headers.Set("Cache-Control", "max-age=0")
		r.Headers.Set("Sec-Fetch-Dest", "document")
		r.Headers.Set("Sec-Fetch-Mode", "navigate")
		r.Headers.Set("Sec-Fetch-Site", "none")
		r.Headers.Set("Sec-Fetch-User", "?1")
		r.Headers.Set("Upgrade-Insecure-Requests", "1")

		logger().Debugf(ctx, "Visiting: %s", r.URL.String())
	})

	// 設置響應回調
	s.collector.OnResponse(func(r *colly.Response) {
		logger().Debugf(ctx, "Response received from %s (status: %d)", r.Request.URL.String(), r.StatusCode)
	})

	// 設置錯誤回調
	s.collector.OnError(func(r *colly.Response, err error) {
		logger().Errorf(ctx, "Error crawling %s: %v (status: %d)", r.Request.URL.String(), err, r.StatusCode)
	})
}

// setupConverter 設置 HTML 到 Markdown 轉換器
func (s *sCollyCrawler) setupConverter(ctx context.Context, collyConfig *CollyConfig) {
	var plugins []converter.Plugin

	// 添加基礎插件
	if collyConfig.HtmlToMarkdown.BaseEnabled {
		plugins = append(plugins, base.NewBasePlugin())
	}

	// 添加 CommonMark 插件
	if collyConfig.HtmlToMarkdown.CommonmarkEnabled {
		plugins = append(plugins, commonmark.NewCommonmarkPlugin())
	}

	// 創建轉換器
	s.converter = converter.NewConverter(
		converter.WithPlugins(plugins...),
	)

	// 如果設置了域名，可以在轉換時使用
	if collyConfig.HtmlToMarkdown.Domain != "" {
		// 域名將在轉換時使用
		logger().Debugf(ctx, "HTML to Markdown converter will use domain: %s", collyConfig.HtmlToMarkdown.Domain)
	}
	logger().Debugf(ctx, "HTML to Markdown converter initialized with escape mode: %s", collyConfig.HtmlToMarkdown.EscapeMode)
}

// isWebContentFromResponse 檢查響應是否為可爬取的網頁內容
func (s *sCollyCrawler) isWebContentFromResponse(response *colly.Response) bool {
	contentType := response.Headers.Get("Content-Type")
	return isWebContent(contentType)
}

// convertHTMLToMarkdown 將 HTML 內容轉換為 Markdown
func (s *sCollyCrawler) convertHTMLToMarkdown(ctx context.Context, htmlContent, pageURL string) (string, error) {
	markdown, err := s.converter.ConvertString(htmlContent)
	if err != nil {
		logger().Errorf(ctx, "Error converting HTML to Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to convert HTML to Markdown: %w", err)
	}
	return markdown, nil
}

// saveMarkdownToFile 將 Markdown 內容保存到文件並返回文件名
func (s *sCollyCrawler) saveMarkdownToFile(ctx context.Context, markdown, pageURL, outputDir string, visitedURLsCount int) (string, error) {
	filename := urlToFilename(pageURL, outputDir, visitedURLsCount)

	err := gfile.PutContents(filename, markdown)
	if err != nil {
		logger().Errorf(ctx, "Error saving Markdown for %s: %v", pageURL, err)
		return "", fmt.Errorf("failed to save Markdown to file: %w", err)
	}

	logger().Debugf(ctx, "Saved Markdown to %s", filename)
	return filename, nil
}

// extractLinksFromHTML 從 HTML 中提取屬於同一域名的鏈接
func (s *sCollyCrawler) extractLinksFromHTML(ctx context.Context, e *colly.HTMLElement, domainName string, visitedURLs map[string]bool) []string {
	var links []string

	e.ForEach("a[href]", func(i int, el *colly.HTMLElement) {
		href := el.Attr("href")
		if href == "" {
			return
		}

		// 如果是相對 URL，轉換為絕對 URL
		if !strings.HasPrefix(href, "http://") && !strings.HasPrefix(href, "https://") {
			href = e.Request.AbsoluteURL(href)
		}

		// 使用共用的 URL 正規化函數
		normalizedURL, err := normalizeURL(href)
		if err != nil {
			logger().Debugf(ctx, "Failed to normalize URL %s: %v", href, err)
			return
		}

		// 解析正規化後的 URL 來檢查域名
		parsedURL, err := url.Parse(normalizedURL)
		if err != nil {
			logger().Debugf(ctx, "Failed to parse normalized URL %s: %v", normalizedURL, err)
			return
		}

		// 檢查是否屬於同一域名
		if !strings.Contains(parsedURL.Host, domainName) {
			return
		}

		// 檢查是否已訪問
		if !visitedURLs[normalizedURL] {
			links = append(links, normalizedURL)
		}
	})

	return links
}

// crawlPageWithColly 使用 Colly 爬取單個頁面
func (s *sCollyCrawler) crawlPageWithColly(
	ctx context.Context,
	pageURL, outputDir string,
	pageToFile *gmap.StrStrMap,
	baseDomainName string,
	visitedManager *VisitedURLManager,
	depth int,
	maxDepth int,
	maxPages int,
	antiBotConfig *AntiBotConfig,
) {
	// 使用共用的訪問檢測
	if !visitedManager.MarkVisited(pageURL) {
		logger().Debugf(ctx, "URL already visited: %s", pageURL)
		return
	}

	if depth > maxDepth {
		logger().Debugf(ctx, "current depth %d over max depth %d then return", depth, maxDepth)
		return
	}
	if visitedManager.Count() > maxPages {
		logger().Debugf(ctx, "current page count %d over max page count %d then return", visitedManager.Count(), maxPages)
		return
	}

	logger().Debugf(ctx, "Crawling: %s", pageURL)

	// 添加隨機延遲以避免被檢測為機器人
	randomDelay(ctx, antiBotConfig)

	// 創建新的收集器實例用於此次爬取
	c := s.collector.Clone()

	var pageContent string
	var links []string

	// 設置 HTML 處理回調
	c.OnHTML("html", func(e *colly.HTMLElement) {
		// 獲取完整的 HTML 內容
		pageContent = string(e.Response.Body)

		// 提取鏈接 - 創建臨時 map 用於兼容現有函數
		tempVisitedMap := make(map[string]bool)
		for _, url := range visitedManager.GetVisitedURLs() {
			tempVisitedMap[url] = true
		}
		links = s.extractLinksFromHTML(ctx, e, baseDomainName, tempVisitedMap)
	})

	// 設置響應處理回調
	c.OnResponse(func(r *colly.Response) {
		// 檢查是否為網頁內容
		if !s.isWebContentFromResponse(r) {
			logger().Infof(ctx, "Skipping non-web content: %s (Content-Type: %s)", r.Request.URL.String(), r.Headers.Get("Content-Type"))
			return
		}

		// 檢查狀態碼
		if r.StatusCode >= 400 {
			logger().Errorf(ctx, "Error: %s returned status code %d", r.Request.URL.String(), r.StatusCode)
			return
		}

		logger().Debugf(ctx, "Successfully crawled %s (status %d)", r.Request.URL.String(), r.StatusCode)
	})

	// 設置錯誤處理回調
	c.OnError(func(r *colly.Response, err error) {
		if r != nil && (r.StatusCode == 403 || r.StatusCode == 429) {
			logger().Infof(ctx, "Detected anti-bot response (status %d) for %s", r.StatusCode, r.Request.URL.String())
		} else {
			logger().Errorf(ctx, "Error crawling %s: %v", pageURL, err)
		}
	})

	// 訪問頁面
	err := c.Visit(pageURL)
	if err != nil {
		logger().Errorf(ctx, "Failed to visit %s: %v", pageURL, err)
		return
	}

	// 等待所有請求完成
	c.Wait()

	// 如果獲取到了頁面內容，進行處理
	if pageContent != "" {
		// 轉換 HTML 為 Markdown
		markdown, err := s.convertHTMLToMarkdown(ctx, pageContent, pageURL)
		if err != nil {
			logger().Errorf(ctx, "Failed to convert HTML to Markdown for %s: %v", pageURL, err)
			return
		}

		// 保存 Markdown 到文件
		filename, err := s.saveMarkdownToFile(ctx, markdown, pageURL, outputDir, visitedManager.Count())
		if err != nil {
			logger().Errorf(ctx, "Failed to save Markdown for %s: %v", pageURL, err)
			return
		}

		// 存儲從 URL 到文件名的映射
		pageToFile.Set(pageURL, filename)

		// 遞歸爬取找到的鏈接
		for _, link := range links {
			s.crawlPageWithColly(ctx, link, outputDir, pageToFile, baseDomainName, visitedManager, depth+1, maxDepth, maxPages, antiBotConfig)
		}
	}
}

// CrawlWebSites 實現 ICrawler 接口的 CrawlWebSites 方法
func (s *sCollyCrawler) CrawlWebSites(ctx context.Context, in *crawl.CrawlWebSiteInput) (err error) {
	logger().Infof(ctx, "crawl web site : %v", gjson.New(in).MustToJsonIndentString())
	if in == nil {
		return fmt.Errorf("input is nil")
	}

	if len(in.WebPageUrls) > 0 {
		var chs = make(chan *website.ModelWebSite, len(in.WebPageUrls))
		var counter = 0
		g.Go(gctx.NeverDone(ctx), func(ctx context.Context) {
			var allWebSiteData = make([]*website.ModelWebSite, 0)
			var quit = false
			for {
				select {
				case webSiteData := <-chs:
					if webSiteData != nil {
						allWebSiteData = append(allWebSiteData, webSiteData)
					}

					counter++
					if counter == len(in.WebPageUrls) {
						logger().Debugf(ctx, "crawl web sites finish...")
						quit = true
						break
					}
				}

				if quit {
					break
				}
			}

			if allWebSiteData != nil && len(allWebSiteData) > 0 {
				e := service.DataSaver().SaveURLContent(ctx, &data_saver.SaveWebPageInput{
					BaseInfo:    in.BaseInfo,
					WebSiteURLs: allWebSiteData,
				})

				if e != nil {
					panic(e)
				}
			}
		}, func(ctx context.Context, exception error) {
			logger().Error(ctx, exception)
			close(chs)
		})

		for _, siteDataWebSite := range in.WebPageUrls {
			if e := s.workerPool.AddWithRecover(gctx.NeverDone(ctx), func(ctx context.Context) {
				webSite, e := s.crawlWebSite(ctx, &crawl.CrawlingWebSiteInput{
					BaseInfo:   in.BaseInfo,
					WebPageUrl: siteDataWebSite,
				})

				if e != nil {
					chs <- nil
				} else {
					chs <- webSite
				}
			}, func(ctx context.Context, exception error) {
				logger().Error(ctx, exception)
			}); e != nil {
				logger().Debugf(ctx, "crawling web site %q failed : %v", siteDataWebSite.URL, e)
				chs <- nil
			}
		}
	} else {
		err = gerror.New("the web site data is empty")
		return
	}

	return
}

// crawlWebSite 爬取單個網站
func (s *sCollyCrawler) crawlWebSite(ctx context.Context, in *crawl.CrawlingWebSiteInput) (webSiteData *website.ModelWebSite, err error) {
	logger().Infof(ctx, "Crawling website : %v", gjson.New(in).MustToJsonIndentString())

	webSiteData = &website.ModelWebSite{DataWebSite: in.WebPageUrl}

	var pageToFile = gmap.NewStrStrMap()

	defer func() {
		if err == nil {
			webSiteData.WebPageFile = pageToFile.Map()
		}
	}()

	// 載入反爬蟲配置
	antiBotConfig := loadAntiBotConfig(ctx)

	// 使用新的訪問管理器
	visitedManager := NewVisitedURLManager()

	domainUrl, err := extractDomainUrl(in.WebPageUrl.URL)
	if err != nil {
		logger().Error(ctx, "crawl website extract domain url failed : %v", err)
		return
	}

	vDir, _ := g.Cfg().Get(ctx, "system.temp_location", "./tmp")

	outputDir := gfile.Join(
		vDir.String(),
		in.TenantID,
		in.ServiceID,
		in.UserID,
		gbase64.EncodeString(in.WebPageUrl.URL),
	)

	vMaxDepth, _ := g.Cfg().Get(ctx, "system.crawling.max_depth", 10)
	vMaxPages, _ := g.Cfg().Get(ctx, "system.crawling.max_pages", 50)

	// 爬取指定的 URL
	s.crawlPageWithColly(ctx, in.WebPageUrl.URL, outputDir, pageToFile, domainUrl, visitedManager, 0, vMaxDepth.Int(), vMaxPages.Int(), antiBotConfig)

	// 如果沒有訪問過根域名，也嘗試爬取
	rootURL := "https://" + domainUrl
	if !visitedManager.IsVisited(rootURL) {
		s.crawlPageWithColly(ctx, rootURL, outputDir, pageToFile, domainUrl, visitedManager, 0, vMaxDepth.Int(), vMaxPages.Int(), antiBotConfig)
	}

	logger().Debugf(ctx, "Crawling completed, visiting %d pages", visitedManager.Count())

	return
}

// Stop 實現 ICrawler 接口的 Stop 方法
func (s *sCollyCrawler) Stop(ctx context.Context) {
	// Colly 不需要特殊的停止操作，但我們可以記錄日誌
	logger().Infof(ctx, "Colly crawler stopped")
}
