2025-08-14T18:34:00.206+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:34:00.207+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.207+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.208+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.208+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:34:00.209+08:00 [DEBU] {38b4ea63ab9b5b18d76078429b4dadcc} [assetManagementService/internal/logic/crawl.randomDelay] common.go:386: Random delay: 163 ms
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.373+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.373+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.374+08:00 [DEBU] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.374+08:00 [INFO] {0040b56dab9b5b18d960784216796097} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.375+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {107ddb6dab9b5b18dc607842f2978ef0} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:34:00.376+08:00 [INFO] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:34:00.376+08:00 [DEBU] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:34:00.376+08:00 [INFO] {a02fe46dab9b5b18dd607842d9290176} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:26.547+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:37:26.548+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:26.548+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:26.548+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:26.548+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:37:26.549+08:00 [INFO] {0809c76edb9b5b187ed7714106c29d2a} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 10, Success: 8 (80.0%), Failed: 1, Skipped: 1, Retries: 2, Duration: 5s
2025-08-14T18:37:26.549+08:00 [INFO] {0809c76edb9b5b187ed7714106c29d2a} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 0, Success: 0 (0.0%), Failed: 0, Skipped: 0, Retries: 0, Duration: 5s
2025-08-14T18:37:33.380+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:37:33.380+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.380+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.381+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.381+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:37:33.381+08:00 [DEBU] {60adff05dd9b5b18c75b050c233f9379} [assetManagementService/internal/logic/crawl.randomDelay] common.go:386: Random delay: 146 ms
2025-08-14T18:37:33.529+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.530+08:00 [DEBU] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.530+08:00 [DEBU] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.530+08:00 [DEBU] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.530+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.531+08:00 [DEBU] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.531+08:00 [INFO] {086ecf0edd9b5b18c95b050c9c2d807d} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.531+08:00 [INFO] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.531+08:00 [DEBU] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.531+08:00 [INFO] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.531+08:00 [INFO] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.531+08:00 [DEBU] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.531+08:00 [INFO] {7079ed0edd9b5b18cc5b050c79dcb2e9} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.531+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.531+08:00 [DEBU] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.532+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.532+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).Stop] colly_crawler.go:452: Colly crawler stopped
2025-08-14T18:37:33.532+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:37:33.532+08:00 [DEBU] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:37:33.532+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:37:33.532+08:00 [INFO] {807cf30edd9b5b18cd5b050cb1cadbb2} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).Stop] colly_crawler.go:452: Colly crawler stopped
2025-08-14T18:37:33.532+08:00 [INFO] {0860fb0edd9b5b18ce5b050cc88510bd} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 10, Success: 8 (80.0%), Failed: 1, Skipped: 1, Retries: 2, Duration: 5s
2025-08-14T18:37:33.532+08:00 [INFO] {0860fb0edd9b5b18ce5b050cc88510bd} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 0, Success: 0 (0.0%), Failed: 0, Skipped: 0, Retries: 0, Duration: 5s
2025-08-14T18:38:44.490+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:38:44.490+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.491+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.491+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.491+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:38:44.491+08:00 [DEBU] {f8977e94ed9b5b186d91914f0e44d393} [assetManagementService/internal/logic/crawl.randomDelay] common.go:386: Random delay: 139 ms
2025-08-14T18:38:44.632+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.633+08:00 [DEBU] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.633+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.633+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.633+08:00 [DEBU] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.633+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.633+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.633+08:00 [DEBU] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.634+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.634+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.634+08:00 [DEBU] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.634+08:00 [INFO] {f801e49ced9b5b186f91914f94376c19} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.637+08:00 [INFO] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.637+08:00 [DEBU] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.637+08:00 [INFO] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.637+08:00 [INFO] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.637+08:00 [DEBU] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.638+08:00 [INFO] {a823269ded9b5b187291914f945dd377} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.638+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.638+08:00 [DEBU] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.638+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.638+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).Stop] colly_crawler.go:452: Colly crawler stopped
2025-08-14T18:38:44.639+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:38:44.639+08:00 [DEBU] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:38:44.639+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:38:44.639+08:00 [INFO] {f803379ded9b5b187391914f6b2dc995} [assetManagementService/internal/logic/crawl.(*sCollyCrawler).Stop] colly_crawler.go:452: Colly crawler stopped
2025-08-14T18:38:44.639+08:00 [INFO] {287e4b9ded9b5b187491914fcd8e98f0} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 10, Success: 8 (80.0%), Failed: 1, Skipped: 1, Retries: 2, Duration: 5s
2025-08-14T18:38:44.639+08:00 [INFO] {287e4b9ded9b5b187491914fcd8e98f0} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 0, Success: 0 (0.0%), Failed: 0, Skipped: 0, Retries: 0, Duration: 5s
2025-08-14T18:40:05.139+08:00 [INFO] [assetManagementService/internal/logic/crawl.InitializeCrawlerEngine] crawler_factory.go:67: Skipping Playwright installation (not required for current engine)
2025-08-14T18:40:05.139+08:00 [INFO] [assetManagementService/internal/logic/crawl.NewCrawler] crawler_factory.go:25: Creating crawler with engine: colly
2025-08-14T18:40:05.140+08:00 [DEBU] [assetManagementService/internal/logic/crawl.(*sCollyCrawler).setupConverter] colly_crawler.go:132: HTML to Markdown converter initialized with escape mode: smart
2025-08-14T18:40:05.140+08:00 [INFO] [assetManagementService/internal/logic/crawl.newCollyCrawler] colly_crawler.go:52: Colly crawler initialized successfully
2025-08-14T18:40:05.140+08:00 [INFO] [assetManagementService/internal/logic/crawl.init.0] crawl.go:22: Crawler service registered successfully
2025-08-14T18:40:05.141+08:00 [INFO] {6093965b009c5b18565bbd62927ead8e} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 10, Success: 8 (80.0%), Failed: 1, Skipped: 1, Retries: 2, Duration: 5s
2025-08-14T18:40:05.141+08:00 [INFO] {6093965b009c5b18565bbd62927ead8e} [assetManagementService/internal/logic/crawl.LogCrawlSummary] common.go:288: [colly] Crawl Summary - Total: 0, Success: 0 (0.0%), Failed: 0, Skipped: 0, Retries: 0, Duration: 5s
