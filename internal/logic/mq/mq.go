package mq

import (
	"assetManagementService/boot"
	"assetManagementService/internal/consts"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	amqp "github.com/rabbitmq/amqp091-go"
)

func init() {
	service.RegisterMessageQueue(New())
}

type sMessageQueue struct {
	conn    *amqp.Connection
	channel *amqp.Channel
}

func FailOnError(err error) {
	if err != nil {
		panic(err)
	}

}
func New() service.IMessageQueue {

	s := &sMessageQueue{}
	ctx := gctx.GetInitCtx()

	boot.WaitReady()

	vUrl, err := g.Cfg().Get(ctx, "rabbitMQ.url", "")
	FailOnError(err)
	if vUrl == nil || vUrl.IsEmpty() {
		panic("rabbitMQ.url is nil")
	}

	s.conn, err = amqp.Dial(vUrl.String())
	FailOnError(err)

	s.channel, err = s.conn.Channel()

	FailOnError(err)
	err = s.channel.ExchangeDeclare(
		consts.ExchangeName,
		"direct",
		true,
		false,
		false,
		false,
		nil,
	)
	FailOnError(err)

	return s

}

func (s *sMessageQueue) logger() glog.ILogger {
	return g.Log().Cat(consts.CatMQ)
}
func (s *sMessageQueue) Send(ctx context.Context, resourceType, action string, data []byte, additionalRouteKeys ...string) (err error) {
	s.logger().Debugf(ctx, "SendToMQ : resource type [%s] action  [%s]  data:%s", resourceType, action, string(data))
	metaData := map[string]interface{}{
		"resource_type": resourceType,
	}

	publishing := amqp.Publishing{
		ContentType: "application/json",
		Body:        data,
		Type:        action,
		Headers:     metaData,
	}

	// 發送到原有的 route key
	err = s.channel.PublishWithContext(
		ctx,
		consts.ExchangeName,
		consts.RouteKey,
		true,
		false,
		publishing,
	)

	if err != nil {
		s.logger().Error(ctx, err)
		return
	}

	// 發送到額外的 route keys
	for _, routeKey := range additionalRouteKeys {
		if err = s.channel.PublishWithContext(
			ctx,
			consts.ExchangeName,
			routeKey,
			true,
			false,
			publishing,
		); err != nil {
			s.logger().Errorf(ctx, "Failed to send to additional route key [%s]: %v", routeKey, err)
			return
		}
		s.logger().Debugf(ctx, "Successfully sent to additional route key [%s]", routeKey)
	}

	return
}
