package consts

const (
	CatStorage               = "storage"
	CatMQ                    = "MQ"
	CatAttachmentsController = "attachments_controller"
	CatHttpLogs              = "http_logs"
	CatalogCrawl             = "crawl"
)
const (
	LocalFS = "localfs"
	GCS     = "gcs"
)

const (
	ResourceTypeFile        = "file"
	ResourceTypeURL         = "url"
	ResourceTypePlainText   = "plain_text"
	ResourceTypeYoutubeLink = "youtube_link"
)

var (
	ServiceName = "ams.svc"
)

const (
	TableResourcesPattern = "%s_resources"
)

// ScriptResource script 腳本 創建保存  resources 的腳本
const ScriptResource = `
 	CREATE TABLE IF NOT EXISTS ` + "`%s`" + `
(
    id                    BIGINT AUTO_INCREMENT PRIMARY KEY,
    service_id            VARCHAR(255) NOT NULL,
    create_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_at             TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    file_storage_provider VARCHAR(50),
    upload_files          JSON,
    url_contents          JSON,
    youtube_contents      JSON,
    plain_text_contents   JSON,

    -- 創建索引
    INDEX idx_service_id (service_id),
    INDEX idx_create_at (create_at),
    INDEX idx_update_at (update_at),
    INDEX idx_file_storage_provider (file_storage_provider)
)
`
const XHeaderService = "X-SERVICE"
