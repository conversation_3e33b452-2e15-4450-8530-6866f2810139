// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"assetManagementService/internal/model"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/resources"
	"context"
)

type (
	IDataSaver interface {
		DownloadFromGCS(ctx context.Context, fileURL string) (filePathName string, err error)
		SaveFile(ctx context.Context, in *data_saver.SaveFileInput) (err error)
		SaveURLContent(ctx context.Context, in *data_saver.SaveWebPageInput) (err error)
		SavePlainTextContent(ctx context.Context, in *data_saver.SavePlainTextInput) (err error)
		SaveYoutubeContent(ctx context.Context, in *data_saver.SaveYoutubeInput) (err error)
		DeleteFile(ctx context.Context, in *data_saver.DeleteFileInput) (err error)
		DeletePlainText(ctx context.Context, in *data_saver.DeletePlainTextInput) (err error)
		DeleteYoutubeLinks(ctx context.Context, in *data_saver.DeleteYoutubeLinksInput) (err error)
		DeleteWebSiteUrls(ctx context.Context, in *data_saver.DeleteWebSiteInput) (err error)
		GetAssets(ctx context.Context, in *model.BaseInfo) (resources []*resources.ResourceRecord, err error)
	}
)

var (
	localDataSaver IDataSaver
)

func DataSaver() IDataSaver {
	if localDataSaver == nil {
		panic("implement not found for interface IDataSaver, forgot register?")
	}
	return localDataSaver
}

func RegisterDataSaver(i IDataSaver) {
	localDataSaver = i
}
