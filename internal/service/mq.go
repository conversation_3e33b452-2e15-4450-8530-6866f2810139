// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IMessageQueue interface {
		Send(ctx context.Context, resourceType string, action string, data []byte, additionalRouteKeys ...string) (err error)
	}
)

var (
	localMessageQueue IMessageQueue
)

func MessageQueue() IMessageQueue {
	if localMessageQueue == nil {
		panic("implement not found for interface IMessageQueue, forgot register?")
	}
	return localMessageQueue
}

func RegisterMessageQueue(i IMessageQueue) {
	localMessageQueue = i
}
