// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"assetManagementService/internal/model/crawl"
	"context"
)

type (
	ICrawler interface {
		CrawlWebSites(ctx context.Context, in *crawl.CrawlWebSiteInput) (err error)
		Stop(ctx context.Context)
	}
)

var (
	localCrawler ICrawler
)

func Crawler() ICrawler {
	if localCrawler == nil {
		panic("implement not found for interface ICrawler, forgot register?")
	}
	return localCrawler
}

func RegisterCrawler(i ICrawler) {
	localCrawler = i
}
