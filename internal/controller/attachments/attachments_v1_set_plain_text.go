package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/model/resources"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) SetPlainText(ctx context.Context, req *v1.SetPlainTextReq) (res *v1.SetPlainTextRes, err error) {
	res = &v1.SetPlainTextRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	cost := gtime.FuncCost(func() {
		if err = service.DataSaver().SavePlainTextContent(ctx, &data_saver.SavePlainTextInput{
			BaseInfo: req.BaseInfo,
			PlainTextContents: []*resources.PlainTextContent{
				{
					PlainText: req.Content,
					Remark:    req.Remark,
				},
			},
		}); err != nil {
			c.logger().Error(ctx, err)
		}
	})
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return

}
