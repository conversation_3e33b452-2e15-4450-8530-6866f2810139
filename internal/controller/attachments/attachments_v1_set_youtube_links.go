package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) SetYoutubeLinks(ctx context.Context, req *v1.SetYoutubeLinksReq) (res *v1.SetYoutubeLinksRes, err error) {
	res = &v1.SetYoutubeLinksRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	cost := gtime.FuncCost(func() {

		if err = service.DataSaver().SaveYoutubeContent(ctx, &data_saver.SaveYoutubeInput{
			BaseInfo:        req.BaseInfo,
			YoutubeContents: req.YoutubeContents,
		}); err != nil {
			c.logger().Error(ctx, err)
		}
	})
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
