package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/service"
	"context"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"

	v1 "assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) UploadFile(ctx context.Context, req *v1.UploadFileReq) (res *v1.UploadFileRes, err error) {
	res = &v1.UploadFileRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	files := make([]string, 0)
	r := g.RequestFromCtx(ctx)

	cost := gtime.FuncCost(func() {
		vTmpLocation, _ := g.Cfg().Get(ctx, "system.temp_location", "")
		var tempLocation = gfile.Temp("ams")
		if !vTmpLocation.IsEmpty() {
			tempLocation = vTmpLocation.String()
		}

		files, err = req.File.Save(tempLocation)
		contentTypes := gstr.Split(req.ContentType, ",")

		if err != nil {
			c.logger().Error(ctx, err)
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()

		} else if len(contentTypes) != len(files) {
			c.logger().Error(ctx, "The content type and the number of files do not match")
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()

		} else {

			saveFileInput := &data_saver.SaveFileInput{}
			saveFileInput.BaseInfo = req.BaseInfo
			saveFileInput.FileContents = make([]*data_saver.FileMetadata, 0)

			for i, file := range files {
				saveFileInput.FileContents = append(saveFileInput.FileContents, &data_saver.FileMetadata{
					FileName:     file,
					FullFileName: gfile.Join(tempLocation, file),
					MimeType:     contentTypes[i],
				})
			}

			if err = service.DataSaver().SaveFile(ctx, saveFileInput); err != nil {
				res.Code = consts.Failed.Code()
				res.Message = consts.Failed.Message()
			}

		}

	})

	res.Cost = cost.String()
	res.FileNames = files

	r.Response.WriteJson(res)

	return
}
