package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/crawl"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) SetWebSite(ctx context.Context, req *v1.SetWebSiteReq) (res *v1.SetWebSiteRes, err error) {
	res = &v1.SetWebSiteRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	cost := gtime.FuncCost(func() {
		if err = service.Crawler().CrawlWebSites(ctx, &crawl.CrawlWebSiteInput{
			BaseInfo:    req.BaseInfo,
			WebPageUrls: req.WebSites,
		}); err != nil {
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()

		}

	})
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
