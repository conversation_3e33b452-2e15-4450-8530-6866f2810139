// =================================================================================
// This is auto-generated by GoFrame CLI tool only once. Fill this file as you wish.
// =================================================================================

package attachments

import (
	"assetManagementService/api/attachments"
	"assetManagementService/internal/consts"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/glog"
)

type ControllerV1 struct{}

func NewV1() attachments.IAttachmentsV1 {
	return &ControllerV1{}
}

func (c *ControllerV1) logger() glog.ILogger {
	return g.Log().Cat(consts.CatAttachmentsController)
}
