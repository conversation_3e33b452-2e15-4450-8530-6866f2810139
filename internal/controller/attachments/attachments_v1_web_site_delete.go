package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"

	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) DeleteWebSite(ctx context.Context, req *v1.DeleteWebSiteReq) (res *v1.DeleteWebSiteRes, err error) {

	res = &v1.DeleteWebSiteRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()
	cost := gtime.FuncCost(func() {
		if err = service.DataSaver().DeleteWebSiteUrls(ctx, &data_saver.DeleteWebSiteInput{
			BaseInfo:    req.BaseInfo,
			WebSiteURLs: req.WebSite,
		}); err != nil {
			c.logger().Error(ctx, err)
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()
		}
	})
	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
