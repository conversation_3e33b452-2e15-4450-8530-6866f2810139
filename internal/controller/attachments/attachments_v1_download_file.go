package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/text/gstr"

	"assetManagementService/api/attachments/v1"
)

// DownloadFile Only downloads of localFS storage are supported. Files saved on GCS can be downloaded through the GCS API.
func (c *ControllerV1) DownloadFile(ctx context.Context, req *v1.DownloadFileReq) (res *v1.DownloadFileRes, err error) {
	c.logger().Infof(ctx, "DownloadFile: %v", gjson.New(req).MustToJsonIndentString())
	r := g.RequestFromCtx(ctx)
	res = &v1.DownloadFileRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	if gstr.HasPrefix(req.FilePath, "gs://") {
		filePathName, err := service.DataSaver().DownloadFromGCS(ctx, req.FilePath)
		if err != nil {
			res.Code = consts.NotFound.Code()
			res.Message = consts.NotFound.Message()
			r.Response.WriteJsonExit(res)
			return nil, err
		}
		r.Response.ServeFileDownload(filePathName)

		_ = gfile.RemoveFile(filePathName)

	} else {
		if !gfile.Exists(req.FilePath) {
			res.Code = consts.NotFound.Code()
			res.Message = consts.NotFound.Message()
			r.Response.WriteJsonExit(res)
		} else {
			r.Response.ServeFileDownload(req.FilePath)
			//_ = gfile.RemoveFile(req.FilePath)
		}

	}

	return
}
