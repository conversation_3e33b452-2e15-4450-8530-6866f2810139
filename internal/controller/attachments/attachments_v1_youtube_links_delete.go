package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) DeleteYoutubeLink(ctx context.Context, req *v1.DeleteYoutubeLinkReq) (res *v1.DeleteYoutubeLinkRes, err error) {
	res = &v1.DeleteYoutubeLinkRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	cost := gtime.FuncCost(func() {
		if err = service.DataSaver().DeleteYoutubeLinks(ctx, &data_saver.DeleteYoutubeLinksInput{
			BaseInfo:     req.BaseInfo,
			YoutubeLinks: req.YoutubeLinks,
		}); err != nil {
			c.logger().Error(ctx, err)
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()
		}
	})

	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
