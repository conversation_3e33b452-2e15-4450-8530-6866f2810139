package attachments

import (
	"assetManagementService/internal/consts"
	"assetManagementService/internal/model/data_saver"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"

	"assetManagementService/api/attachments/v1"
)

func (c *ControllerV1) DeletePlainText(ctx context.Context, req *v1.DeletePlainTextReq) (res *v1.DeletePlainTextRes, err error) {
	res = &v1.DeletePlainTextRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	cost := gtime.FuncCost(func() {
		if err = service.DataSaver().DeletePlainText(ctx, &data_saver.DeletePlainTextInput{
			BaseInfo:         req.BaseInfo,
			PlainTextContent: req.Content,
		}); err != nil {
			c.logger().Error(ctx, err)
			res.Code = consts.Failed.Code()
			res.Message = consts.Failed.Message()
		}

	})

	res.Cost = cost.String()
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return

}
