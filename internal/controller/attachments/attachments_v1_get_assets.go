package attachments

import (
	"assetManagementService/api/attachments/v1"
	"assetManagementService/internal/consts"
	"assetManagementService/internal/service"
	"context"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
)

func (c *ControllerV1) GetAssets(ctx context.Context, req *v1.GetAssetsReq) (res *v1.GetAssetsRes, err error) {
	res = &v1.GetAssetsRes{}
	res.Code = consts.Success.Code()
	res.Message = consts.Success.Message()

	cost := gtime.FuncCost(func() {
		res.Assets, err = service.DataSaver().GetAssets(ctx, &req.BaseInfo)

	})

	res.Cost = cost.String()
	if err != nil {
		res.Code = consts.Failed.Code()
		res.Message = err.Error()
	}
	g.RequestFromCtx(ctx).Response.WriteJson(res)
	return
}
