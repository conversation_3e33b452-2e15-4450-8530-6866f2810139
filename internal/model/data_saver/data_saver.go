package data_saver

import (
	. "assetManagementService/internal/model"
	"assetManagementService/internal/model/resources"
	"assetManagementService/internal/model/website"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
)

type FileMetadata struct {
	FileName     string `json:"file_name"`
	FullFileName string `json:"full_file_name"`
	MimeType     string `json:"content_type"`
}

type SaveFileInput struct {
	BaseInfo
	FileContents []*FileMetadata `json:"file_contents"`
}

type SaveWebPageInput struct {
	BaseInfo
	WebSiteURLs []*website.ModelWebSite `json:"web_site_ur_ls"`
}

type SavePlainTextInput struct {
	BaseInfo
	PlainTextContents []*resources.PlainTextContent
}

type SaveYoutubeInput struct {
	BaseInfo
	YoutubeContents []*resources.YoutubeContent
}
type DeleteFileInput struct {
	BaseInfo
	Files []string `json:"files"`
}
type DeletePlainTextInput struct {
	BaseInfo
	PlainTextContent string `json:"plain_text_content"`
}
type DeleteYoutubeLinksInput struct {
	BaseInfo
	YoutubeLinks []string `json:"youtube_links"`
}
type DeleteWebSiteInput struct {
	BaseInfo
	WebSiteURLs []string `json:"web_site_urls"`
}

type GetContentsReq struct {
	Schema    string  `json:"schema"`
	Table     string  `json:"table"`
	WhereCond string  `json:"where_cond"`
	Params    g.Slice `json:"params"`
	Fields    g.Slice `json:"fields"`
	Limit     int     `json:"limit"`
	RawSQL    string  `json:"raw_sql"`
	Order     string  `json:"order"`
}

type GetContentsRes struct {
	Contents []map[string]any `json:"contents"`
}
type MQDeleteContentData struct {
	Schema    string   `json:"schema"`
	Table     string   `json:"table"`
	ServiceID string   `json:"service_id"`
	UserID    string   `json:"user_id"`
	Contents  []string `json:"files"`
}

func (m *MQDeleteContentData) String() string {
	return gjson.New(m).MustToJsonIndentString()
}
