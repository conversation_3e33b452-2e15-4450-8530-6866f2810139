package resources

import (
	"time"

	"github.com/gogf/gf/v2/encoding/gjson"
)

type FileContent struct {
	FileName      string `json:"file_name"`
	MimeType      string `json:"content_type"`
	Size          int64  `json:"size"`
	StorageBucket string `json:"storage_bucket"`
	AccessUrl     string `json:"access_url"`
	AccessPath    string `json:"access_path"`
}

type WebSiteURLContent struct {
	WebSiteURL        string                  `json:"website_url"`
	PageToFileContent map[string]*FileContent `json:"page_to_file_content"` // web page url to FileContent
}

type YoutubeContent struct {
	YoutubeLink string `json:"youtube_link" v:"required"`
	Remark      string `json:"remark"`
}

type PlainTextContent struct {
	PlainText string `json:"plain_text"`
	Remark    string `json:"remark"`
}

type ResourceRecord struct {
	Schema string `json:"schema"`
	Table  string `json:"table"`

	ServiceID string    `json:"service_id"`
	ID        string    `json:"id"`
	CreateAT  time.Time `json:"create_at"` //timestamp
	UpdateAT  time.Time `json:"update_at"` // time stamp

	FileStorageProvider string               `json:"file_storage_provider"` // "GCS ,localFS"
	UploadFiles         []*FileContent       `json:"upload_files"`
	URLContents         []*WebSiteURLContent `json:"url_contents"`
	YoutubeContents     []*YoutubeContent    `json:"youtube_contents"`
	PlainTextContents   []*PlainTextContent  `json:"plain_text_contents"`
}

func (r *ResourceRecord) String() string {
	return gjson.New(r).MustToJsonIndentString()
}
